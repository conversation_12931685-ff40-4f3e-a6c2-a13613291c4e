import logging

class BaseAction:
    """Base class for all action handlers"""

    def __init__(self, controller=None):
        """
        Initialize the action handler

        Args:
            controller: The device controller to use for actions
        """
        self.controller = controller
        self.logger = logging.getLogger(self.__class__.__name__)

        # Initialize method selector for strategic method selection
        try:
            from ..utils.method_selector import method_selector
            self.method_selector = method_selector
        except ImportError:
            # Fallback if import fails
            self.method_selector = None
            self.logger.warning("Method selector not available, using default method selection")

    def execute(self, params):
        """
        Execute the action with the given parameters

        Args:
            params: Dictionary of parameters for the action

        Returns:
            dict: Result of the action with status and message
        """
        raise NotImplementedError("Subclasses must implement execute method")

    def set_controller(self, controller):
        """
        Update the controller for the action handler

        Args:
            controller: The device controller to use
        """
        self.controller = controller

    def get_global_timeout(self, default=90):
        """
        Get the global element timeout value from the database with enhanced defaults

        Best Practice: Use 90+ second timeouts for Android stability and reliability

        Args:
            default: Default timeout value if global setting can't be retrieved (minimum 60s)

        Returns:
            int: The global timeout value in seconds (minimum 60s for Android)
        """
        try:
            try:
                from ..utils.global_values_db import GlobalValuesDB
            except ImportError:
                import sys
                import os
                current_dir = os.path.dirname(os.path.abspath(__file__))
                parent_dir = os.path.dirname(current_dir)
                if parent_dir not in sys.path:
                    sys.path.insert(0, parent_dir)
                from app_android.utils.global_values_db import GlobalValuesDB
            global_values_db = GlobalValuesDB()
            timeout = global_values_db.get_value('default_element_timeout', default)
            timeout_value = int(timeout)

            # Enforce minimum timeout for Android stability (best practice)
            if timeout_value < 60:
                self.logger.info(f"Increasing timeout from {timeout_value}s to 60s for Android stability")
                return 60

            return timeout_value
        except Exception as e:
            self.logger.warning(f"Could not get default timeout from global settings: {e}")
            # Ensure minimum default timeout
            return max(default, 60)

    def take_screenshot_after_action(self):
        """Take a screenshot after action execution if controller supports it"""
        if not self.controller:
            return None

        try:
            screenshot_path = self.controller.take_screenshot()
            return screenshot_path
        except Exception as e:
            self.logger.error(f"Error taking screenshot after action: {e}")
            return None

    def cleanup_debug_images(self):
        """Clean up debug images created during action execution"""
        try:
            # Clean up debug images from the controller's image matcher if available
            if hasattr(self.controller, 'image_matcher') and self.controller.image_matcher:
                self.controller.image_matcher.cleanup_debug_images()
                self.logger.debug("Cleaned up debug images from image matcher")

            # Also clean up any debug images from temp directory
            from ..utils.file_utils import cleanup_temp_files
            cleanup_temp_files(max_age_hours=1)  # Clean files older than 1 hour

        except Exception as e:
            self.logger.warning(f"Error during debug image cleanup: {e}")

    def execute_with_cleanup(self, params):
        """
        Execute the action with automatic debug image cleanup

        Args:
            params: Dictionary of parameters for the action

        Returns:
            dict: Result of the action with status and message
        """
        try:
            # Execute the main action
            result = self.execute(params)
            return result
        finally:
            # Always clean up debug images after action execution
            self.cleanup_debug_images()

    def is_conditional_action(self):
        """
        Check if this is a conditional action (tap if exists, check if exists, etc.)
        Conditional actions should use fast, lightweight operations with no retries

        Returns:
            bool: True if this is a conditional action
        """
        action_class_name = self.__class__.__name__.lower()
        conditional_patterns = [
            'tapif', 'checkif', 'ifexists', 'conditional'
        ]
        return any(pattern in action_class_name for pattern in conditional_patterns)

    def execute_with_retry(self, operation_func, max_retries=3, retry_delay=2, operation_name="operation", action_timeout=None):
        """
        Execute an operation with retry mechanism for better stability and timeout compliance
        Optimized for conditional actions to use single attempt with no retries

        Args:
            operation_func: Function to execute
            max_retries: Maximum number of retry attempts (ignored for conditional actions)
            retry_delay: Delay between retries in seconds (ignored for conditional actions)
            operation_name: Name of the operation for logging
            action_timeout: Original action timeout in seconds (for compliance enforcement)

        Returns:
            Result of the operation or error dict
        """
        import time
        from requests.exceptions import ConnectionError, ReadTimeout
        from selenium.common.exceptions import WebDriverException

        # Optimize for conditional actions - use single attempt with no retries
        if self.is_conditional_action():
            self.logger.info(f"Conditional action detected - using single attempt for {operation_name}")
            try:
                return operation_func()
            except Exception as e:
                self.logger.debug(f"Conditional action {operation_name} failed (single attempt): {e}")
                # For conditional actions, return success even on failure (if exists behavior)
                return {
                    "status": "success",
                    "message": f"Conditional action {operation_name} completed (if exists behavior)"
                }

        # Standard retry logic for non-conditional actions
        action_start_time = time.time()
        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                # Check timeout compliance before each attempt
                if action_timeout:
                    elapsed_time = time.time() - action_start_time
                    remaining_timeout = action_timeout - elapsed_time

                    if remaining_timeout <= 0:
                        self.logger.warning(f"{operation_name} timeout exceeded ({action_timeout}s) before attempt {attempt + 1}")
                        return {
                            "status": "error",
                            "message": f"Action timeout ({action_timeout}s) exceeded during retry attempts"
                        }

                    self.logger.info(f"{operation_name} attempt {attempt + 1}: {remaining_timeout:.1f}s remaining of {action_timeout}s timeout")

                if attempt > 0:
                    self.logger.info(f"Retry attempt {attempt}/{max_retries} for {operation_name}")
                    time.sleep(retry_delay)

                result = operation_func()

                if attempt > 0:
                    self.logger.info(f"{operation_name} succeeded on retry attempt {attempt}")

                return result

            except (ConnectionError, ReadTimeout, WebDriverException) as e:
                last_exception = e
                error_msg = str(e)

                # Check timeout compliance before recovery
                if action_timeout:
                    elapsed_time = time.time() - action_start_time
                    remaining_timeout = action_timeout - elapsed_time

                    if remaining_timeout <= retry_delay:
                        self.logger.warning(f"{operation_name} insufficient time for recovery: {remaining_timeout:.1f}s remaining")
                        return {
                            "status": "error",
                            "message": f"Action timeout ({action_timeout}s) exceeded - insufficient time for session recovery"
                        }

                # Check for specific timeout errors
                if "Read timed out" in error_msg or "HTTPConnectionPool" in error_msg:
                    self.logger.warning(f"{operation_name} failed with timeout error (attempt {attempt + 1}/{max_retries + 1}): {error_msg}")

                    # Try to reconnect if controller supports it and we have time
                    if hasattr(self.controller, 'reconnect_if_needed'):
                        try:
                            recovery_start = time.time()
                            self.controller.reconnect_if_needed()
                            recovery_time = time.time() - recovery_start
                            self.logger.info(f"Session recovery took {recovery_time:.1f}s")
                        except Exception as reconnect_e:
                            self.logger.warning(f"Reconnection attempt failed: {reconnect_e}")
                else:
                    self.logger.warning(f"{operation_name} failed (attempt {attempt + 1}/{max_retries + 1}): {error_msg}")

                if attempt == max_retries:
                    break

                # Check timeout compliance before retry delay
                if action_timeout:
                    elapsed_time = time.time() - action_start_time
                    remaining_timeout = action_timeout - elapsed_time

                    if remaining_timeout <= retry_delay:
                        self.logger.warning(f"{operation_name} insufficient time for retry delay: {remaining_timeout:.1f}s remaining")
                        break

            except Exception as e:
                # For non-retryable exceptions, fail immediately
                self.logger.error(f"{operation_name} failed with non-retryable error: {e}")
                return {
                    "status": "error",
                    "message": f"{operation_name} failed: {str(e)}"
                }

        # All retries exhausted
        return {
            "status": "error",
            "message": f"{operation_name} failed after {max_retries + 1} attempts. Last error: {str(last_exception)}"
        }

    def should_use_airtest_for_action(self, action_type, operation_type=None):
        """
        Determine if AirTest should be used for this action

        Args:
            action_type (str): The type of action being performed
            operation_type (str): The specific operation within the action (optional)

        Returns:
            bool: True if AirTest should be used, False otherwise
        """
        if self.method_selector:
            return self.method_selector.should_use_airtest(action_type, operation_type)

        # Fallback logic if method selector is not available
        # Only use AirTest for explicit text/image operations
        airtest_operations = ['text_recognition', 'image_recognition', 'find_text', 'find_image']
        return operation_type in airtest_operations if operation_type else False

    def get_preferred_automation_method(self, action_type, operation_type=None, available_methods=None):
        """
        Get the preferred automation method for this action

        Args:
            action_type (str): The type of action being performed
            operation_type (str): The specific operation within the action (optional)
            available_methods (list): List of available methods

        Returns:
            str: The preferred method to use
        """
        if self.method_selector:
            return self.method_selector.get_preferred_method(action_type, operation_type, available_methods)

        # Fallback logic
        if available_methods is None:
            available_methods = ['uiautomator', 'appium', 'adb']

        return available_methods[0] if available_methods else 'uiautomator'

    def log_method_selection(self, action_type, operation_type, selected_method, reason=""):
        """
        Log the method selection decision

        Args:
            action_type (str): The type of action being performed
            operation_type (str): The specific operation within the action
            selected_method (str): The method that was selected
            reason (str): Additional reason for the selection
        """
        if self.method_selector:
            self.method_selector.log_method_selection(action_type, operation_type, selected_method, reason)
        else:
            self.logger.info(f"Method Selection: {action_type}/{operation_type} -> {selected_method} ({reason})")

    def scale_ios_coordinates(self, coordinates, device_info=None):
        """
        Scale coordinates for iOS devices to match the UI scale

        This implements similar scaling logic to the client-side JavaScript:
        const scaleX = this.deviceScreen.naturalWidth / rect.width;
        const scaleY = this.deviceScreen.naturalHeight / rect.height;

        Args:
            coordinates: Tuple of (x, y) coordinates to scale
            device_info: Optional device info to use for scaling factors

        Returns:
            Tuple of scaled (x, y) coordinates
        """
        from airtest.core.helper import G

        # If not iOS or no coordinates, return as is
        if not hasattr(self.controller, 'platform_name') or self.controller.platform_name != 'iOS':
            return coordinates

        if not coordinates or len(coordinates) != 2:
            return coordinates

        # Get original coordinates
        original_x, original_y = coordinates

        # Try to get device dimensions from controller
        device_dimensions = None
        if hasattr(self.controller, 'get_device_dimensions'):
            try:
                device_dimensions = self.controller.get_device_dimensions()
                self.logger.info(f"Device physical dimensions: {device_dimensions}")
            except Exception as dim_err:
                self.logger.warning(f"Could not get device dimensions: {dim_err}")

        # Try to get UI dimensions from Airtest
        ui_dimensions = None
        try:
            if hasattr(G, 'DEVICE') and G.DEVICE and hasattr(G.DEVICE, 'display_info'):
                display_info = G.DEVICE.display_info
                if display_info and 'width' in display_info and 'height' in display_info:
                    ui_dimensions = (display_info['width'], display_info['height'])
                    self.logger.info(f"Device UI dimensions: {ui_dimensions}")
        except Exception as e:
            self.logger.warning(f"Could not get Airtest display info: {e}")

        # If we have both dimensions, calculate actual scaling factor
        if device_dimensions and ui_dimensions and device_dimensions[0] > 0 and device_dimensions[1] > 0:
            # This matches the client-side scaling logic
            scale_x = ui_dimensions[0] / device_dimensions[0]
            scale_y = ui_dimensions[1] / device_dimensions[1]

            scaled_x = int(original_x * scale_x)
            scaled_y = int(original_y * scale_y)

            self.logger.info(f"iOS coordinate scaling: ({original_x}, {original_y}) -> ({scaled_x}, {scaled_y})")
            return (scaled_x, scaled_y)
        else:
            # Apply a fixed scaling factor based on the examples given
            # Appears to be approximately 1/3 (from 651->214, 2623->876)
            scale_factor = 0.33

            scaled_x = int(original_x * scale_factor)
            scaled_y = int(original_y * scale_factor)

            self.logger.info(f"iOS coordinate scaling (fixed): ({original_x}, {original_y}) -> ({scaled_x}, {scaled_y})")
            return (scaled_x, scaled_y)

    def find_element_fast_conditional(self, locator_type, locator_value, timeout=10):
        """
        Fast element finding method optimized for conditional actions
        Uses minimal timeout, no retries, no context switching, no health checks

        Args:
            locator_type: Type of locator (id, xpath, accessibility_id, uiselector, etc.)
            locator_value: Value of the locator
            timeout: Timeout for element finding (default: 10 seconds)

        Returns:
            WebElement or None if not found
        """
        if not self.controller or not hasattr(self.controller, 'driver') or not self.controller.driver:
            self.logger.debug("No Appium driver available for fast conditional finding")
            return None

        self.logger.debug(f"Fast conditional element finding: {locator_type}='{locator_value}' (timeout: {timeout}s)")

        try:
            # Handle UISelector locator type first (Android specific)
            if locator_type.lower() == 'uiselector':
                self.logger.debug(f"Using UISelector for fast conditional finding: {locator_value}")
                return self._find_element_by_uiselector_fast(locator_value, timeout)

            # Use controller's fast element finding if available for standard locators
            if hasattr(self.controller, 'find_element_fast'):
                return self.controller.find_element_fast(locator_type, locator_value, timeout)

            # Fallback to basic WebDriverWait with presence condition only
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.webdriver.common.by import By
            from selenium.common.exceptions import TimeoutException

            # Map locator types to By methods
            by_mapping = {
                'id': By.ID,
                'xpath': By.XPATH,
                'accessibility_id': By.ACCESSIBILITY_ID,
                'class_name': By.CLASS_NAME,
                'tag_name': By.TAG_NAME,
                'name': By.NAME,
                'css_selector': By.CSS_SELECTOR,
                'link_text': By.LINK_TEXT,
                'partial_link_text': By.PARTIAL_LINK_TEXT
            }

            by_type = by_mapping.get(locator_type.lower())
            if not by_type:
                self.logger.debug(f"Unsupported locator type for fast conditional finding: {locator_type}")
                return None

            # Use only presence condition for speed (no visibility/clickable checks)
            wait = WebDriverWait(self.controller.driver, timeout)
            element = wait.until(EC.presence_of_element_located((by_type, locator_value)))
            self.logger.debug(f"Fast conditional element found: {locator_type}='{locator_value}'")
            return element

        except TimeoutException:
            self.logger.debug(f"Fast conditional element not found within {timeout}s: {locator_type}='{locator_value}'")
            return None
        except Exception as e:
            self.logger.debug(f"Fast conditional element finding failed: {e}")
            return None

    def _find_element_by_uiselector_fast(self, uiselector_value, timeout):
        """
        Fast UISelector element finding for conditional actions

        Args:
            uiselector_value: UISelector string
            timeout: Timeout for element finding

        Returns:
            WebElement or None if not found
        """
        try:
            # Import AppiumBy for proper UIAutomator2 usage
            try:
                from appium.webdriver.common.appiumby import AppiumBy
                locator_type = AppiumBy.ANDROID_UIAUTOMATOR
            except ImportError:
                # Fallback to MobileBy for older Appium versions
                from appium.webdriver.common.mobileby import MobileBy
                locator_type = MobileBy.ANDROID_UIAUTOMATOR

            # Use WebDriverWait for fast conditional finding
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            from selenium.common.exceptions import TimeoutException

            wait = WebDriverWait(self.controller.driver, timeout)
            element = wait.until(EC.presence_of_element_located((locator_type, uiselector_value)))
            self.logger.debug(f"Fast UISelector element found: {uiselector_value}")
            return element

        except TimeoutException:
            self.logger.debug(f"Fast UISelector element not found within {timeout}s: {uiselector_value}")
            return None
        except Exception as e:
            self.logger.debug(f"Fast UISelector element finding failed: {e}")
            return None

    def find_element_with_locator(self, locator_type, locator_value, timeout=None):
        """
        Universal method to find elements using various locator types including UISelector
        with intelligent WebView context switching
        Automatically suspends health checks during operation to prevent interference

        Args:
            locator_type: Type of locator (id, xpath, accessibility_id, uiselector, etc.)
            locator_value: Value of the locator
            timeout: Optional timeout for element finding

        Returns:
            WebElement or None if not found
        """
        if not self.controller or not hasattr(self.controller, 'driver') or not self.controller.driver:
            self.logger.error("No Appium driver available")
            return None

        timeout = timeout or self.get_global_timeout()

        # For conditional actions, use fast finding method
        if self.is_conditional_action():
            self.logger.info(f"Using fast conditional element finding for {locator_type}='{locator_value}' (timeout: {timeout}s)")
            return self.find_element_fast_conditional(locator_type, locator_value, timeout)

        # For TapIfLocatorExists actions, respect user-specified timeout to prevent hangs
        # Only enforce minimum timeout for critical operations
        action_type = getattr(self, 'action_type', None)
        if action_type == 'tapIfLocatorExists':
            # Respect user timeout for conditional actions to prevent hangs
            self.logger.info(f"Using user-specified timeout {timeout}s for TapIfLocatorExists action")
        elif timeout < 30:
            # Minimum 30 seconds for other operations (reduced from 60 for better responsiveness)
            timeout = 30
            self.logger.info(f"Increased timeout to {timeout} seconds for Android stability")

        # Suspend health checks during element detection to prevent interference
        health_checks_were_suspended = getattr(self.controller, 'health_checks_suspended', False)
        if not health_checks_were_suspended:
            self._suspend_health_checks_for_operation()

        try:
            # Set implicit wait for better element detection reliability
            self._set_implicit_wait(timeout)

            # Use intelligent context switching for element detection
            return self._find_element_with_context_switching(locator_type, locator_value, timeout)

        finally:
            # Resume health checks if we suspended them
            if not health_checks_were_suspended:
                self._resume_health_checks_for_operation()

    def _set_implicit_wait(self, timeout):
        """
        Set implicit wait for better element detection reliability

        Best Practice: Use implicit wait to automatically retry element finding

        Args:
            timeout: Timeout value in seconds
        """
        try:
            if hasattr(self.controller, 'driver') and self.controller.driver:
                self.controller.driver.implicitly_wait(timeout)
                self.logger.debug(f"Set implicit wait to {timeout} seconds")
        except Exception as e:
            self.logger.warning(f"Failed to set implicit wait: {e}")

    def _find_element_with_context_switching(self, locator_type, locator_value, timeout):
        """
        Find element with intelligent context switching between NATIVE and WebView contexts
        Includes circuit breaker patterns to prevent infinite loops and hangs

        Logic Flow:
        1. First attempt: Search for element in NATIVE context for the specified timeout duration
        2. If element not found: Automatically switch to WebView context and search for the same element for an additional timeout period
        3. If WebView context doesn't exist or element still not found: Mark step as FAILED
        4. Always switch back to NATIVE context after the operation (whether successful or failed)

        Args:
            locator_type: Type of locator (id, xpath, accessibility_id, uiselector, etc.)
            locator_value: Value of the locator
            timeout: Timeout for element finding (will be split between contexts)

        Returns:
            WebElement or None if not found
        """
        import time
        start_time = time.time()
        max_total_time = timeout * 2  # Maximum total time for both contexts

        # Circuit breaker: Track context switching attempts to prevent infinite loops
        max_context_switches = 3
        context_switch_count = 0

        original_context = None
        element = None
        context_health_checks_suspended = False

        try:
            # Circuit breaker: Check if context switching is already in progress
            if hasattr(self.controller, '_context_switching_in_progress') and self.controller._context_switching_in_progress:
                self.logger.warning("Context switching already in progress, using current context only")
                return self._find_element_in_current_context(locator_type, locator_value, timeout)

            # Set context switching flag
            self.controller._context_switching_in_progress = True

            # Suspend health checks during context switching operations
            context_health_checks_suspended = getattr(self.controller, 'health_checks_suspended', False)
            if not context_health_checks_suspended:
                self._suspend_health_checks_for_operation()

            # Get current context with timeout protection
            try:
                original_context = self.controller.driver.current_context
                self.logger.info(f"Current context: {original_context}")
            except Exception as e:
                self.logger.error(f"Failed to get current context: {e}")
                return None

            # Ensure we start in NATIVE context
            if not original_context or 'NATIVE' not in original_context:
                context_switch_count += 1
                if context_switch_count > max_context_switches:
                    self.logger.error("Maximum context switches exceeded, aborting")
                    return None

                if not self._switch_to_native_context_with_timeout(5):  # 5 second timeout
                    self.logger.error("Failed to switch to NATIVE context")
                    return None

                try:
                    original_context = self.controller.driver.current_context
                except Exception as e:
                    self.logger.error(f"Failed to get context after switch: {e}")
                    return None

            # First attempt: Search in NATIVE context
            self.logger.info(f"Attempting to find element in NATIVE context: {locator_type}='{locator_value}'")
            element = self._find_element_in_current_context(locator_type, locator_value, timeout)

            if element:
                self.logger.info(f"Element found in NATIVE context")
                return element

            # Check if we have time left for WebView context search
            elapsed_time = time.time() - start_time
            if elapsed_time >= max_total_time:
                self.logger.warning(f"Total timeout ({max_total_time}s) exceeded, skipping WebView context search")
                return None

            # Second attempt: Search in WebView context
            remaining_time = max_total_time - elapsed_time
            webview_timeout = min(timeout, remaining_time)

            self.logger.info(f"Element not found in NATIVE context, attempting WebView context switch (timeout: {webview_timeout}s)")

            context_switch_count += 1
            if context_switch_count > max_context_switches:
                self.logger.error("Maximum context switches exceeded, aborting WebView search")
                return None

            webview_switched = self._switch_to_webview_context_with_timeout(5)  # 5 second timeout

            if webview_switched:
                self.logger.info(f"Attempting to find element in WebView context: {locator_type}='{locator_value}'")
                element = self._find_element_in_current_context(locator_type, locator_value, webview_timeout)

                if element:
                    self.logger.info(f"Element found in WebView context")
                    return element
                else:
                    self.logger.warning(f"Element not found in WebView context either")
            else:
                self.logger.warning(f"WebView context not available or switch failed")

            return None

        except Exception as e:
            self.logger.error(f"Error during context switching element detection: {e}")
            return None

        finally:
            # Clear context switching flag
            if hasattr(self.controller, '_context_switching_in_progress'):
                self.controller._context_switching_in_progress = False

            # Resume health checks if we suspended them during context switching
            if not context_health_checks_suspended:
                self._resume_health_checks_for_operation()

            # Always switch back to NATIVE context with timeout protection
            try:
                context_switch_count += 1
                if context_switch_count <= max_context_switches:
                    self._switch_to_native_context_with_timeout(5)
                    self.logger.info("Switched back to NATIVE context")
                else:
                    self.logger.warning("Skipping final context switch due to circuit breaker")
            except Exception as e:
                self.logger.error(f"Failed to switch back to NATIVE context: {e}")

    def _find_element_in_current_context(self, locator_type, locator_value, timeout):
        """
        Find element in the current context without context switching

        Args:
            locator_type: Type of locator
            locator_value: Value of the locator
            timeout: Timeout for element finding

        Returns:
            WebElement or None if not found
        """
        # Enhanced retry mechanism with exponential backoff (best practice)
        max_retries = 5  # Increased retries for better reliability
        base_retry_delay = 2
        import time

        # Apply locator optimization before attempting to find element
        optimized_type, optimized_value, suggestions = self._optimize_locator_strategy(locator_type, locator_value)
        # Use optimized locator if different from original
        if optimized_type != locator_type or optimized_value != locator_value:
            locator_type, locator_value = optimized_type, optimized_value
            self.logger.info(f"Using optimized locator: {locator_type}='{locator_value}'")

        for attempt in range(max_retries):
            try:
                self.logger.info(f"Finding element attempt {attempt + 1}/{max_retries}: {locator_type}='{locator_value}'")

                # Handle UISelector locator type
                if locator_type.lower() == 'uiselector':
                    element = self._find_element_by_uiselector(locator_value, timeout // max_retries)
                else:
                    # Handle standard locator types
                    element = self._find_element_by_standard_locator(locator_type, locator_value, timeout // max_retries)

                if element:
                    self.logger.info(f"Element found successfully on attempt {attempt + 1}")
                    return element

            except Exception as e:
                error_msg = str(e).lower()
                self.logger.warning(f"Attempt {attempt + 1} failed: {e}")

                # Check for connection-related errors that might need session recovery
                if any(keyword in error_msg for keyword in ['timeout', 'connection', 'socket', 'http', 'session']):
                    self.logger.warning("Connection-related error detected, attempting session recovery")
                    if hasattr(self.controller, 'reconnect_if_needed'):
                        try:
                            self.controller.reconnect_if_needed()
                        except Exception as recovery_error:
                            self.logger.error(f"Session recovery failed: {recovery_error}")

                # If this is not the last attempt, wait before retrying with exponential backoff
                if attempt < max_retries - 1:
                    # Exponential backoff: 2s, 4s, 8s, 16s
                    retry_delay = base_retry_delay * (2 ** attempt)
                    self.logger.info(f"Waiting {retry_delay} seconds before retry (exponential backoff)...")
                    time.sleep(retry_delay)
                else:
                    self.logger.error(f"All {max_retries} attempts failed for {locator_type}='{locator_value}'")

        return None

    def _switch_to_native_context(self):
        """
        Switch to native context

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            contexts = self.controller.driver.contexts
            native_context = None

            for ctx in contexts:
                if 'NATIVE_APP' in ctx:
                    native_context = ctx
                    break

            if native_context:
                self.controller.driver.switch_to.context(native_context)
                self.logger.info(f"Switched to native context: {native_context}")
                return True
            else:
                self.logger.warning("Native context not found")
                return False

        except Exception as e:
            self.logger.error(f"Failed to switch to native context: {e}")
            return False

    def _switch_to_webview_context(self):
        """
        Switch to WebView context

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            contexts = self.controller.driver.contexts
            webview_context = None

            for ctx in contexts:
                if 'WEBVIEW' in ctx:
                    webview_context = ctx
                    break

            if webview_context:
                self.controller.driver.switch_to.context(webview_context)
                self.logger.info(f"Switched to WebView context: {webview_context}")
                return True
            else:
                self.logger.info("WebView context not available")
                return False

        except Exception as e:
            self.logger.error(f"Failed to switch to WebView context: {e}")
            return False

    def _switch_to_native_context_with_timeout(self, timeout_seconds=5):
        """
        Switch to native context with timeout protection to prevent hangs

        Args:
            timeout_seconds: Maximum time to wait for context switch

        Returns:
            bool: True if successful, False otherwise
        """
        import threading
        import time

        result = [False]  # Use list to allow modification in nested function

        def switch_context():
            try:
                contexts = self.controller.driver.contexts
                native_context = None

                for ctx in contexts:
                    if 'NATIVE_APP' in ctx:
                        native_context = ctx
                        break

                if native_context:
                    self.controller.driver.switch_to.context(native_context)
                    self.logger.info(f"Switched to native context: {native_context}")
                    result[0] = True
                else:
                    self.logger.warning("Native context not found")
            except Exception as e:
                self.logger.error(f"Failed to switch to native context: {e}")

        # Execute context switch in a separate thread with timeout
        switch_thread = threading.Thread(target=switch_context)
        switch_thread.daemon = True
        switch_thread.start()
        switch_thread.join(timeout=timeout_seconds)

        if switch_thread.is_alive():
            self.logger.error(f"Context switch to native timed out after {timeout_seconds} seconds")
            return False

        return result[0]

    def _switch_to_webview_context_with_timeout(self, timeout_seconds=5):
        """
        Switch to WebView context with timeout protection to prevent hangs

        Args:
            timeout_seconds: Maximum time to wait for context switch

        Returns:
            bool: True if successful, False otherwise
        """
        import threading
        import time

        result = [False]  # Use list to allow modification in nested function

        def switch_context():
            try:
                contexts = self.controller.driver.contexts
                webview_context = None

                for ctx in contexts:
                    if 'WEBVIEW' in ctx:
                        webview_context = ctx
                        break

                if webview_context:
                    self.controller.driver.switch_to.context(webview_context)
                    self.logger.info(f"Switched to WebView context: {webview_context}")
                    result[0] = True
                else:
                    self.logger.info("WebView context not available")
            except Exception as e:
                self.logger.error(f"Failed to switch to WebView context: {e}")

        # Execute context switch in a separate thread with timeout
        switch_thread = threading.Thread(target=switch_context)
        switch_thread.daemon = True
        switch_thread.start()
        switch_thread.join(timeout=timeout_seconds)

        if switch_thread.is_alive():
            self.logger.error(f"Context switch to WebView timed out after {timeout_seconds} seconds")
            return False

        return result[0]

    def _suspend_health_checks_for_operation(self):
        """
        Suspend health checks during critical element detection operations
        Enhanced for conditional actions to be more aggressive
        """
        try:
            if hasattr(self.controller, '_suspend_health_checks'):
                self.controller._suspend_health_checks()
                if self.is_conditional_action():
                    self.logger.debug("Health checks aggressively suspended for conditional action")
                else:
                    self.logger.debug("Health checks suspended for element detection operation")

            # Additional suspension for conditional actions
            if self.is_conditional_action() and hasattr(self.controller, 'health_checks_suspended'):
                self.controller.health_checks_suspended = True

        except Exception as e:
            self.logger.warning(f"Could not suspend health checks: {e}")

    def _resume_health_checks_for_operation(self):
        """
        Resume health checks after critical element detection operations complete
        """
        try:
            if hasattr(self.controller, '_resume_health_checks'):
                self.controller._resume_health_checks()
                if self.is_conditional_action():
                    self.logger.debug("Health checks resumed after conditional action")
                else:
                    self.logger.debug("Health checks resumed after element detection operation")

            # Additional resume for conditional actions
            if self.is_conditional_action() and hasattr(self.controller, 'health_checks_suspended'):
                self.controller.health_checks_suspended = False

        except Exception as e:
            self.logger.warning(f"Could not resume health checks: {e}")

    def _optimize_locator_strategy(self, locator_type, locator_value):
        """
        Optimize locator strategy based on best practices

        Best Practices:
        1. Prioritize ID and Accessibility ID over XPath
        2. Use relative XPath instead of absolute XPath
        3. Optimize UISelector for better performance

        Args:
            locator_type: Original locator type
            locator_value: Original locator value

        Returns:
            tuple: (optimized_locator_type, optimized_locator_value, suggestions)
        """
        suggestions = []
        optimized_type = locator_type
        optimized_value = locator_value

        # XPath optimization
        if locator_type.lower() == 'xpath':
            if locator_value.startswith('/html') or locator_value.count('/') > 5:
                suggestions.append("Consider using ID or Accessibility ID instead of complex XPath")
                suggestions.append("If XPath is necessary, use relative XPath starting with '//'")

                # Try to suggest a relative XPath if it's absolute
                if locator_value.startswith('/html'):
                    # Extract the last meaningful part for relative XPath suggestion
                    parts = locator_value.split('/')
                    if len(parts) > 2:
                        last_part = parts[-1]
                        if '[' in last_part or '@' in last_part:
                            optimized_value = f"//{last_part}"
                            suggestions.append(f"Suggested relative XPath: {optimized_value}")

        # UISelector optimization
        elif locator_type.lower() == 'uiselector':
            if 'index(' in locator_value:
                suggestions.append("Avoid using index() in UISelector as it's fragile to UI changes")
            if not any(method in locator_value for method in ['resourceId(', 'text(', 'contentDescription(']):
                suggestions.append("Consider using resourceId(), text(), or contentDescription() for more stable UISelector")

        # ID locator optimization
        elif locator_type.lower() == 'id':
            if ':id/' not in locator_value and not locator_value.startswith('android:id/'):
                # Suggest full resource ID format
                suggestions.append("Consider using full resource ID format: 'package:id/resource_name'")

        # Log suggestions if any
        if suggestions:
            self.logger.info(f"Locator optimization suggestions for {locator_type}='{locator_value}':")
            for suggestion in suggestions:
                self.logger.info(f"  - {suggestion}")

        return optimized_type, optimized_value, suggestions

    def _find_element_by_uiselector(self, uiselector_value, timeout):
        """
        Find element using UISelector (Android UIAutomator)

        Args:
            uiselector_value: UISelector string
            timeout: Timeout for element finding

        Returns:
            WebElement or None if not found
        """
        try:
            # Import AppiumBy for proper UIAutomator2 usage
            try:
                from appium.webdriver.common.appiumby import AppiumBy
                locator_type = AppiumBy.ANDROID_UIAUTOMATOR
            except ImportError:
                # Fallback to MobileBy for older Appium versions
                from appium.webdriver.common.mobileby import MobileBy
                locator_type = MobileBy.ANDROID_UIAUTOMATOR

            # Use WebDriverWait for better reliability
            if self._has_selenium_support():
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC

                wait = WebDriverWait(self.controller.driver, timeout)
                element = wait.until(EC.presence_of_element_located((locator_type, uiselector_value)))
                return element
            else:
                # Direct find without wait
                return self.controller.driver.find_element(locator_type, uiselector_value)

        except Exception as e:
            self.logger.warning(f"UISelector element finding failed: {e}")
            return None

    def _find_element_by_standard_locator(self, locator_type, locator_value, timeout):
        """
        Find element using standard locator types

        Args:
            locator_type: Standard locator type (id, xpath, accessibility_id, etc.)
            locator_value: Locator value
            timeout: Timeout for element finding

        Returns:
            WebElement or None if not found
        """
        try:
            # Special handling for Android ID locators with fallback mechanisms
            if locator_type.lower() == 'id' and hasattr(self.controller, 'platform_name') and self.controller.platform_name and self.controller.platform_name.lower() == 'android':
                return self._find_element_by_android_id_with_fallback(locator_value, timeout)

            # Use AppiumBy for proper Android/iOS locator handling
            if self._has_selenium_support():
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC

                # Import AppiumBy for proper mobile locator handling
                try:
                    from appium.webdriver.common.appiumby import AppiumBy

                    locator_map = {
                        'id': AppiumBy.ID,  # Properly handles Android resource-id
                        'xpath': AppiumBy.XPATH,
                        'name': AppiumBy.NAME,
                        'class': AppiumBy.CLASS_NAME,
                        'class_name': AppiumBy.CLASS_NAME,
                        'accessibility_id': AppiumBy.ACCESSIBILITY_ID,
                        'android_uiautomator': AppiumBy.ANDROID_UIAUTOMATOR,
                        'ios_predicate': AppiumBy.IOS_PREDICATE,
                        'ios_class_chain': AppiumBy.IOS_CLASS_CHAIN,
                        'css_selector': AppiumBy.CSS_SELECTOR,
                        'link_text': AppiumBy.LINK_TEXT,
                        'partial_link_text': AppiumBy.PARTIAL_LINK_TEXT,
                        'tag_name': AppiumBy.TAG_NAME
                    }
                except ImportError:
                    # Fallback to older MobileBy for compatibility
                    from appium.webdriver.common.mobileby import MobileBy
                    from selenium.webdriver.common.by import By

                    locator_map = {
                        'id': MobileBy.ID,
                        'xpath': By.XPATH,
                        'name': By.NAME,
                        'class': By.CLASS_NAME,
                        'class_name': By.CLASS_NAME,
                        'accessibility_id': MobileBy.ACCESSIBILITY_ID,
                        'android_uiautomator': MobileBy.ANDROID_UIAUTOMATOR,
                        'ios_predicate': MobileBy.IOS_PREDICATE,
                        'ios_class_chain': MobileBy.IOS_CLASS_CHAIN
                    }

                by_type = locator_map.get(locator_type.lower())
                if not by_type:
                    # Default to xpath if locator type not found
                    by_type = locator_map.get('xpath', AppiumBy.XPATH if 'AppiumBy' in locals() else By.XPATH)
                    self.logger.warning(f"Unknown locator type '{locator_type}', using XPath as fallback")

                wait = WebDriverWait(self.controller.driver, timeout)
                element = wait.until(EC.presence_of_element_located((by_type, locator_value)))
                return element
            else:
                # Direct find without wait - fallback for older versions
                # Use modern find_element method with AppiumBy
                try:
                    from appium.webdriver.common.appiumby import AppiumBy

                    locator_map = {
                        'id': AppiumBy.ID,
                        'xpath': AppiumBy.XPATH,
                        'accessibility_id': AppiumBy.ACCESSIBILITY_ID,
                        'class': AppiumBy.CLASS_NAME,
                        'class_name': AppiumBy.CLASS_NAME,
                        'name': AppiumBy.NAME
                    }

                    by_type = locator_map.get(locator_type.lower())
                    if by_type:
                        return self.controller.driver.find_element(by_type, locator_value)
                    else:
                        self.logger.warning(f"Unsupported locator type for fallback: {locator_type}")
                        return None

                except ImportError:
                    # Very old fallback using deprecated methods
                    if locator_type.lower() == 'id':
                        return self.controller.driver.find_element_by_id(locator_value)
                    elif locator_type.lower() == 'xpath':
                        return self.controller.driver.find_element_by_xpath(locator_value)
                    elif locator_type.lower() == 'accessibility_id':
                        return self.controller.driver.find_element_by_accessibility_id(locator_value)
                    elif locator_type.lower() in ['class', 'class_name']:
                        return self.controller.driver.find_element_by_class_name(locator_value)
                    elif locator_type.lower() == 'name':
                        return self.controller.driver.find_element_by_name(locator_value)
                    else:
                        self.logger.warning(f"Unsupported locator type for fallback: {locator_type}")
                        return None

        except Exception as e:
            # Provide detailed error information for debugging
            error_msg = f"Standard locator element finding failed for {locator_type}='{locator_value}': {e}"

            # Add specific guidance for common Android ID locator issues
            if locator_type.lower() == 'id':
                error_msg += f"\n  Hint: For Android, ensure the ID value is the full resource-id (e.g., 'com.app:id/button')"
                error_msg += f"\n  Hint: You can also try using 'accessibility_id' locator type instead"
                error_msg += f"\n  Hint: Consider using UI Selector: new UiSelector().resourceId('{locator_value}')"
                error_msg += f"\n  Hint: The framework tried multiple ID formats automatically - check logs for details"

            self.logger.warning(error_msg)
            return None

    def _find_element_by_android_id_with_fallback(self, locator_value, timeout):
        """
        Find element by Android ID with multiple fallback strategies

        Args:
            locator_value: ID value to search for
            timeout: Timeout for element finding

        Returns:
            WebElement or None if not found
        """
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC

        # Import AppiumBy for proper mobile locator handling
        try:
            from appium.webdriver.common.appiumby import AppiumBy
            id_locator_type = AppiumBy.ID
        except ImportError:
            from appium.webdriver.common.mobileby import MobileBy
            id_locator_type = MobileBy.ID

        # Strategy 1: Try the original ID value as provided
        try:
            self.logger.info(f"Android ID Strategy 1: Trying original ID value: '{locator_value}'")
            wait = WebDriverWait(self.controller.driver, timeout // 3)  # Use 1/3 of timeout for each strategy
            element = wait.until(EC.presence_of_element_located((id_locator_type, locator_value)))
            self.logger.info(f"Android ID Strategy 1 successful: Found element with ID '{locator_value}'")
            return element
        except Exception as e:
            self.logger.info(f"Android ID Strategy 1 failed: {e}")

        # Strategy 2: If the ID doesn't contain a package, try common package prefixes
        if ':id/' not in locator_value and '/' not in locator_value:
            common_prefixes = [
                'android:id/',
                f'{self._get_app_package()}:id/' if self._get_app_package() else None,
                'com.android.systemui:id/',
                'com.google.android:id/'
            ]

            for prefix in common_prefixes:
                if prefix:
                    full_id = f"{prefix}{locator_value}"
                    try:
                        self.logger.info(f"Android ID Strategy 2: Trying with prefix: '{full_id}'")
                        wait = WebDriverWait(self.controller.driver, timeout // 6)  # Use smaller timeout for each prefix
                        element = wait.until(EC.presence_of_element_located((id_locator_type, full_id)))
                        self.logger.info(f"Android ID Strategy 2 successful: Found element with ID '{full_id}'")
                        return element
                    except Exception as e:
                        self.logger.info(f"Android ID Strategy 2 failed for '{full_id}': {e}")

        # Strategy 3: If the ID contains a package, try just the ID part
        if ':id/' in locator_value:
            short_id = locator_value.split(':id/')[-1]
            try:
                self.logger.info(f"Android ID Strategy 3: Trying short ID: '{short_id}'")
                wait = WebDriverWait(self.controller.driver, timeout // 3)
                element = wait.until(EC.presence_of_element_located((id_locator_type, short_id)))
                self.logger.info(f"Android ID Strategy 3 successful: Found element with short ID '{short_id}'")
                return element
            except Exception as e:
                self.logger.info(f"Android ID Strategy 3 failed: {e}")

        # Strategy 4: Try using UI Selector as fallback
        try:
            self.logger.info(f"Android ID Strategy 4: Trying UI Selector fallback for ID: '{locator_value}'")
            uiselector_value = f'new UiSelector().resourceId("{locator_value}")'
            element = self._find_element_by_uiselector(uiselector_value, timeout // 3)
            if element:
                self.logger.info(f"Android ID Strategy 4 successful: Found element using UI Selector")
                return element
        except Exception as e:
            self.logger.info(f"Android ID Strategy 4 failed: {e}")

        self.logger.warning(f"All Android ID strategies failed for: '{locator_value}'")
        return None

    def _get_app_package(self):
        """Get the current app package name"""
        try:
            if hasattr(self.controller, 'driver') and self.controller.driver:
                return self.controller.driver.current_package
        except Exception as e:
            self.logger.debug(f"Could not get app package: {e}")
        return None

    def _has_selenium_support(self):
        """Check if Selenium WebDriverWait is available"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            return True
        except ImportError:
            return False