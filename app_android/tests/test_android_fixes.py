#!/usr/bin/env python3
"""
Comprehensive test suite for Android mobile app testing fixes.
Tests the three critical fixes implemented:
1. Execute All button functionality
2. Test case expand/collapse UI controls
3. Feature parity with iOS version
"""

import unittest
import os
import sys
import json
import re
from pathlib import Path

# Add the parent directory to the path so we can import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestAndroidFixes(unittest.TestCase):
    """Test suite for Android mobile app testing fixes."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.android_template_path = Path(__file__).parent.parent / "templates" / "index.html"
        self.android_js_path = Path(__file__).parent.parent / "static" / "js"
        self.ios_template_path = Path(__file__).parent.parent.parent / "app" / "templates" / "index.html"
        
        # Read template files
        with open(self.android_template_path, 'r', encoding='utf-8') as f:
            self.android_template = f.read()
        
        with open(self.ios_template_path, 'r', encoding='utf-8') as f:
            self.ios_template = f.read()
    
    def test_execute_all_button_exists(self):
        """Test that Execute All button exists in Android template."""
        self.assertIn('id="executeAllBtn"', self.android_template)
        self.assertIn('Execute All', self.android_template)
    
    def test_expand_collapse_buttons_exist(self):
        """Test that expand/collapse buttons exist in Android template."""
        # Check for expand button
        self.assertIn('id="expandAllBtn"', self.android_template)
        self.assertIn('bi-arrows-expand', self.android_template)
        self.assertIn('Expand All Test Cases', self.android_template)
        
        # Check for collapse button
        self.assertIn('id="collapseAllBtn"', self.android_template)
        self.assertIn('bi-arrows-collapse', self.android_template)
        self.assertIn('Collapse All Test Cases', self.android_template)
    
    def test_execution_manager_js_exists(self):
        """Test that execution-manager.js file exists and contains proper binding."""
        execution_manager_path = self.android_js_path / "execution-manager.js"
        self.assertTrue(execution_manager_path.exists(), "execution-manager.js should exist")
        
        with open(execution_manager_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for proper event listener binding
        self.assertIn('.bind(this)', content, "Execute All button should use proper binding")
        self.assertIn('executeAllActions.bind(this)', content, "executeAllActions should be bound to this")
    
    def test_main_js_expand_collapse_functionality(self):
        """Test that main.js contains expand/collapse functionality."""
        main_js_path = self.android_js_path / "main.js"
        self.assertTrue(main_js_path.exists(), "main.js should exist")
        
        with open(main_js_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for expand/collapse methods
        self.assertIn('expandAllTestCases', content, "expandAllTestCases method should exist")
        self.assertIn('collapseAllTestCases', content, "collapseAllTestCases method should exist")
        
        # Check for event listeners
        self.assertIn('expandAllBtn', content, "expandAllBtn event listener should exist")
        self.assertIn('collapseAllBtn', content, "collapseAllBtn event listener should exist")
    
    def test_ios_functions_action_form_exists(self):
        """Test that iOS Functions action form exists in Android template."""
        self.assertIn('id="iosFunctionsActionForm"', self.android_template)
        self.assertIn('iOS Functions Action Form', self.android_template)
        self.assertIn('iosFunctions', self.android_template)
    
    def test_ios_functions_dropdown_option_exists(self):
        """Test that iOS Functions option exists in action type dropdown."""
        # Look for the iOS Functions option in the action type dropdown
        ios_functions_pattern = r'<option\s+value="iosFunctions"[^>]*>iOS Functions</option>'
        self.assertRegex(self.android_template, ios_functions_pattern, 
                        "iOS Functions option should exist in action type dropdown")
    
    def test_ios_specific_locators_exist(self):
        """Test that iOS-specific locators exist in Android template."""
        # Check for iOS Class Chain
        self.assertIn('ios_class_chain', self.android_template)
        self.assertIn('iOS Class Chain', self.android_template)
        
        # Check for iOS Predicate
        self.assertIn('ios_predicate', self.android_template)
        self.assertIn('iOS Predicate', self.android_template)
    
    def test_tap_and_type_ios_action_exists(self):
        """Test that Tap and Type (iOS) action exists."""
        self.assertIn('tapAndType', self.android_template)
        self.assertIn('Tap and Type (iOS)', self.android_template)
    
    def test_bootstrap_collapse_functionality(self):
        """Test that Bootstrap collapse functionality is properly implemented."""
        # Check for Bootstrap collapse classes and attributes
        self.assertIn('collapse', self.android_template)
        self.assertIn('data-bs-toggle="collapse"', self.android_template)
        self.assertIn('bootstrap.Collapse', self.android_template)
    
    def test_video_recording_capability_exists(self):
        """Test that video recording capability exists in Android version."""
        # Check for video recording related code
        self.assertIn('video', self.android_template.lower())
        self.assertIn('recording', self.android_template.lower())
    
    def test_test_retry_mechanism_exists(self):
        """Test that test retry mechanism exists."""
        # Check for retry-related functionality
        self.assertIn('retry', self.android_template.lower())
        self.assertIn('hookAction', self.android_template)
    
    def test_feature_parity_core_functionality(self):
        """Test that core functionality has feature parity between iOS and Android."""
        # Core action types that should exist in both
        core_actions = [
            'tap', 'doubleTap', 'swipe', 'text', 'wait', 
            'takeScreenshot', 'exists', 'waitTill'
        ]
        
        for action in core_actions:
            self.assertIn(f'value="{action}"', self.android_template, 
                         f"Core action '{action}' should exist in Android")
    
    def test_platform_specific_features_properly_marked(self):
        """Test that platform-specific features are properly marked."""
        # Check for iOS-only class markers
        self.assertIn('ios-only', self.android_template)
        
        # Check for Android-only class markers
        self.assertIn('android-only', self.android_template)
    
    def test_javascript_syntax_validity(self):
        """Test that JavaScript files have valid syntax (basic check)."""
        js_files = [
            self.android_js_path / "main.js",
            self.android_js_path / "execution-manager.js"
        ]
        
        for js_file in js_files:
            if js_file.exists():
                with open(js_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Basic syntax checks
                self.assertEqual(content.count('{'), content.count('}'), 
                               f"Mismatched braces in {js_file}")
                self.assertEqual(content.count('('), content.count(')'), 
                               f"Mismatched parentheses in {js_file}")
    
    def test_html_template_validity(self):
        """Test that HTML template has valid structure (basic check)."""
        # Basic HTML structure checks
        self.assertIn('<!DOCTYPE html>', self.android_template)
        self.assertIn('<html', self.android_template)
        self.assertIn('</html>', self.android_template)
        self.assertIn('<head>', self.android_template)
        self.assertIn('</head>', self.android_template)
        self.assertIn('<body>', self.android_template)
        self.assertIn('</body>', self.android_template)
    
    def test_critical_ui_elements_exist(self):
        """Test that all critical UI elements exist."""
        critical_elements = [
            'id="actionsList"',
            'id="executeAllBtn"',
            'id="expandAllBtn"',
            'id="collapseAllBtn"',
            'class="test-case-container"',
            'class="test-case-header"',
            'class="test-case-actions"'
        ]
        
        for element in critical_elements:
            self.assertIn(element, self.android_template, 
                         f"Critical UI element '{element}' should exist")


if __name__ == '__main__':
    # Run the tests
    unittest.main(verbosity=2)
